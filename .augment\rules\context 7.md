---
type: "always_apply"
description: "globs:"
---
# 开发工作流程规则

## Context7 优先原则

在进行任何开发任务时，augment每条指令都必须执行下述步骤：
必须严格遵循以下原则：

### 1. 强制使用Context7进行组件调研
- **编写代码之前**，必须使用Context7工具调查将要使用的组件、库或框架的用法
- 不允许基于假设或记忆来编写代码
- 必须获取最新的文档和示例代码
- 对于不确定的API或组件属性，必须先通过Context7澄清

### 2. 澄清优先原则
- 遇到任何不确定的技术细节时，不允许进行假设
- 必须通过以下方式进行澄清：
  - 使用Context7查询相关文档
  - 使用web_search获取最新信息
  - 向用户明确询问具体需求

### 3. 工作流程步骤
1. **分析任务** - 识别需要使用的技术栈和组件
2. **Context7调研** - 查询相关组件和库的使用方法
3. **澄清需求** - 确认所有不明确的技术细节
4. **编写代码** - 基于调研结果实现功能

### 4. 禁止行为
- ❌ 不允许基于记忆编写代码
- ❌ 不允许假设API接口或组件属性
- ❌ 不允许跳过Context7调研步骤
- ❌ 不允许在不确定的情况下继续开发

## 示例工作流程

用户: "请帮我创建一个Vue组件"
AI: 
1. 首先使用Context7查询Vue 3组件最佳实践
2. 查询Nuxt 3组件开发指南
3. 澄清具体的组件需求和功能
4. 基于调研结果编写组件

请严格遵循这些规则来确保代码质量和开发效率。